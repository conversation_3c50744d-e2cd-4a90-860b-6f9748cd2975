const LexJobCreationWorker = require("./lexJobCreationWorker");
const LexReviewFetchWorker = require("./lexReviewFetchWorker");
const LexAiAnalysisWorker = require("./lexAiAnalysisWorker");
const cron = require("node-cron");
const { sendServerNotification } = require("../utils/slack");

// Singleton instance to prevent multiple worker managers
let instance = null;

class LexWorkerManager {
  constructor() {
    // Singleton pattern - return existing instance if it exists
    if (instance) {
      console.log(
        "⚠️ LexWorkerManager instance already exists, returning existing instance"
      );
      return instance;
    }

    console.log("🔧 Creating new LexWorkerManager instance...");
    this.serverRestarting = false;
    this.jobCreationWorker = new LexJobCreationWorker(this); // Pass reference to this manager
    this.reviewFetchWorker = new LexReviewFetchWorker(this); // Pass reference to this manager
    // this.aiWorker = new LexAiAnalysisWorker();
    this.isRunning = false;
    this.isProcessingCycle = false; // Flag to prevent overlapping cycles
    this.cronJob = null; // Store the cron job reference

    // Store this instance as the singleton
    instance = this;
  }

  // Static method to get the singleton instance
  static getInstance() {
    if (!instance) {
      instance = new LexWorkerManager();
    }
    return instance;
  }

  // Static method to check if instance exists
  static hasInstance() {
    return instance !== null;
  }

  // Static method to reset the singleton (for testing or cleanup)
  static resetInstance() {
    instance = null;
  }

  getServerRestarting() {
    return this.serverRestarting;
  }

  setServerRestarting(value) {
    this.serverRestarting = value;
  }

  async start() {
    if (this.isRunning) {
      console.log("⚠️ Worker manager is already running");
      return;
    }

    console.log("🚀 Starting Unified Lex Worker Manager...");
    console.log("📋 This manager will run a coordinated workflow:");
    console.log(
      "  - Unified cron for Job Creation → Review Fetch (every 2 minutes)"
    );
    console.log("  - Separate AI Analysis Worker (every 2 minutes)");
    console.log("  - No race conditions between Job Creation and Review Fetch");
    console.log("");

    try {
      // Initialize workers without starting their individual crons
      await this.initializeWorkers();

      // Start the unified cron orchestrator for Job Creation + Review Fetch
      this.startUnifiedCron();

      // Start AI Analysis Worker separately
      // await this.aiWorker.start();

      this.isRunning = true;
      console.log("✅ Unified worker orchestrator started successfully!");
      console.log("");
      console.log("📊 Orchestrator Status:");
      console.log(
        "  - Unified Cron: Running (Job Creation → Review Fetch every 2 minutes)"
      );
      console.log(
        "  - Job Creation Worker: Initialized (runs on-demand in unified cycle)"
      );
      console.log(
        "  - Review Fetch Worker: Initialized (runs on-demand in unified cycle)"
      );
      console.log(
        "  - AI Analysis Worker: Running independently (every 2 minutes)"
      );
      console.log("");
      console.log("🔄 Unified Cycle: Job Creation → Review Fetch");
      console.log("🤖 AI Analysis: Runs independently");
      console.log("Press Ctrl+C to stop all workers gracefully...");

      // Send notification that Lex Worker Manager is ready
      await sendServerNotification("START", "Lex Worker Manager initialized and ready");
    } catch (error) {
      console.error("❌ Error starting unified worker manager:", error);
      this.isRunning = false;
    }
  }

  /**
   * Initialize workers without starting their individual cron jobs
   */
  async initializeWorkers() {
    console.log("🔧 Initializing workers...");
    // Workers are already initialized in constructor
    // We don't call their start() methods to avoid individual cron jobs
    console.log("✅ Workers initialized successfully");
  }

  /**
   * Start the unified cron job that orchestrates Job Creation and Review Fetch sequentially
   */
  startUnifiedCron() {
    console.log(
      "⏰ Starting unified cron orchestrator for Job Creation + Review Fetch..."
    );

    // Run every 2 minutes - coordinates Job Creation → Review Fetch
    this.cronJob = cron.schedule("*/2 * * * *", async () => {
      if (this.isProcessingCycle) {
        console.log("⏳ Previous unified cycle still running, skipping...");
        return;
      }

      if (this.serverRestarting) {
        console.log("🔄 Server restart in progress, skipping unified cycle...");
        return;
      }

      try {
        this.isProcessingCycle = true;
        await this.runUnifiedCycle();
      } catch (error) {
        console.error("❌ Error in unified cycle:", error);
      } finally {
        this.isProcessingCycle = false;
      }
    });

    console.log(
      "✅ Unified cron orchestrator scheduled (Job Creation → Review Fetch every 2 minutes)"
    );
  }

  /**
   * Run the unified cycle: Job Creation → Review Fetch (AI Analysis runs separately)
   */
  async runUnifiedCycle() {
    const cycleStart = Date.now();
    console.log("\n🔄 Starting unified cycle (Job Creation → Review Fetch)...");

    try {
      // Step 1: Job Creation (creates new scraping jobs)
      console.log("📝 Step 1: Running Job Creation Worker...");
      await this.jobCreationWorker.processJobs();
      console.log("✅ Job Creation completed");

      // Step 2: Review Fetch (processes existing jobs and fetches reviews)
      console.log("📥 Step 2: Running Review Fetch Worker...");
      await this.reviewFetchWorker.processReviews();
      console.log("✅ Review Fetch completed");

      const cycleTime = ((Date.now() - cycleStart) / 1000).toFixed(2);
      console.log(`🎯 Unified cycle completed successfully in ${cycleTime}s`);
      console.log("🤖 AI Analysis continues running independently\n");
    } catch (error) {
      const cycleTime = ((Date.now() - cycleStart) / 1000).toFixed(2);
      console.error(
        `❌ Unified cycle failed after ${cycleTime}s:`,
        error.message
      );
      throw error;
    }
  }

  async stop() {
    if (!this.isRunning) {
      console.log("⚠️ Worker manager is not running");
      return;
    }

    console.log("🛑 Stopping Unified Lex Worker Manager...");

    try {
      // Stop the unified cron job
      if (this.cronJob) {
        this.cronJob.stop();
        console.log("⏰ Unified cron orchestrator stopped");
      }

      // Wait for current cycle to complete if running
      if (this.isProcessingCycle) {
        console.log("⏳ Waiting for current worker cycle to complete...");
        while (this.isProcessingCycle) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
        console.log("✅ Current worker cycle completed");
      }

      // Stop individual workers if they have their own cleanup
      if (this.jobCreationWorker.stop) {
        await this.jobCreationWorker.stop();
      }
      if (this.reviewFetchWorker.stop) {
        await this.reviewFetchWorker.stop();
      }
      // if (this.aiWorker.stop) {
      //   await this.aiWorker.stop();
      // }

      this.isRunning = false;

      // Reset singleton instance on stop
      instance = null;

      console.log("✅ Unified worker manager stopped gracefully");
      
      // Send notification that Lex Worker Manager has stopped
      await sendServerNotification("STOP", "Lex Worker Manager stopped gracefully");
    } catch (error) {
      console.error("❌ Error stopping unified worker manager:", error);
      // Reset singleton instance even on error
      instance = null;
    }
  }

  getStatus() {
    return {
      isRunning: this.isRunning,
      workers: {
        jobCreation: !!this.jobCreationWorker,
        reviewFetch: !!this.reviewFetchWorker,
        aiAnalysis: !!this.aiWorker,
      },
    };
  }

  async getAnalysisStats() {
    if (this.aiWorker) {
      return await this?.aiWorker?.getAnalysisStats();
    }
    return null;
  }
}

// Worker manager is now controlled by the scheduler
// No direct execution when this file is run

module.exports = LexWorkerManager;
