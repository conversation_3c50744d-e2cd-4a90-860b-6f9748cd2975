generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["metrics"]
  binaryTargets   = ["native", "debian-openssl-1.1.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id              Int              @id @default(autoincrement())
  name            String
  email           String           @unique
  password        String
  userType        String
  slug            String           @default("")
  signature       String           @default("")
  logo            String           @default("")
  ctaLink         String           @default("")
  bgColor         String           @default("")
  caseStudies     Json             @default("{}")
  testimonials    J<PERSON>             @default("[]")
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  mbLogo          String           @default("")
  tableLogo       String           @default("")
  googleSheetURL  String           @default("")
  offerLine       String           @default("")
  website         String           @default("")
  caseStudiesText String           @default("Read full case study here")
  ctaButtonText   String           @default("")
  calendlyId      String           @default("")
  Company         Company[]
  Configurations  Configurations[]
  Job             Job[]

  @@index([email])
}

model Configurations {
  id                 Int      @id @default(autoincrement())
  clientId           Int
  scraperApi         Json     @default("{}")
  jungleScout        Json     @default("{}")
  openAi             Json     @default("{}")
  caseStudies        Json     @default("{}")
  compEmailTemplates Json     @default("{}")
  auditEmailPrompts  Json     @default("{}")
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  promptTemplates    Json     @default("{}")
  singleEntryOpt     Boolean  @default(false)
  scrapingBeeApi     Json     @default("{}")
  user               User     @relation(fields: [clientId], references: [id], onDelete: Cascade)

  @@index([clientId])
}

model Company {
  id                Int                 @id @default(autoincrement())
  name              String
  clientId          Int
  website           String
  jobId             Int
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  productUrl        String              @default("")
  searchUrl         String              @default("")
  storeFrontURL     String              @default("")
  AboutData         AboutData[]
  AmazonAuditReport AmazonAuditReport[]
  AmazonProductData AmazonProductData[]
  user              User                @relation(fields: [clientId], references: [id], onDelete: Cascade)
  job               Job                 @relation(fields: [jobId], references: [id], onDelete: Cascade)
  CompetitionData   CompetitionData[]
  OutputData        OutputData[]
  ppcData           PPCData[]

  @@index([jobId])
}

model Job {
  id             Int          @id @default(autoincrement())
  clientId       Int
  status         String
  name           String
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  singleCompany  Boolean      @default(false)
  ppcAudit       Boolean      @default(false)
  auditPdf       Boolean      @default(false)
  campaignId     Int?
  qualifiedLeads Int          @default(0)
  statusMap      Json?        @default("{}")
  Company        Company[]
  Campaign       Campaign?    @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  user           User         @relation(fields: [clientId], references: [id], onDelete: Cascade)
  OutputData     OutputData[]

  @@index([status])
  @@index([clientId])
  @@index([campaignId])
}

model JobCentral {
  id         Int       @id @default(autoincrement())
  scriptType String
  params     Json
  queueName  String
  clientId   Int
  priority   Int
  status     JobStatus @default(PENDING)
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @default(now())

  @@index([scriptType])
  @@index([clientId])
}

model Campaign {
  id       Int    @id @default(autoincrement())
  campaign String
  Jobs     Job[]
}

model AboutData {
  id           Int      @id @default(autoincrement())
  companyId    Int
  website      String
  aboutData    String
  homepageData String
  status       String
  aboutUrls    String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  company      Company  @relation(fields: [companyId], references: [id], onDelete: Cascade)

  @@index([companyId])
}

model AmazonProductData {
  id         Int      @id @default(autoincrement())
  data       Json     @default("{}")
  companyId  Int
  status     String   @default("")
  searchUrl  String   @default("")
  slug       String   @default("")
  pushedToSB Boolean  @default(false)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  validTill  DateTime @default(now())
  company    Company  @relation(fields: [companyId], references: [id], onDelete: Cascade)

  @@index([companyId])
  @@index([slug])
}

model CompetitionData {
  id                         Int      @id @default(autoincrement())
  data                       String
  companyId                  Int
  status                     String   @default("")
  compKeyPrompt              String   @default("")
  searchKeyword              String   @default("")
  prospectProductName        String   @default("")
  prospectRevenue            Float    @default(0.0)
  competitorRevenue          Float    @default(0.0)
  competitorProductName      String   @default("")
  competitorName             String   @default("")
  competitorProductAmazonURL String   @default("")
  prospectProductAmazonURL   String   @default("")
  prospectRevenueSource      String   @default("")
  createdAt                  DateTime @default(now())
  updatedAt                  DateTime @default(now())
  company                    Company  @relation(fields: [companyId], references: [id], onDelete: Cascade)

  @@index([companyId])
}

model PPCData {
  id                      Int      @id @default(autoincrement())
  companyId               Int
  slug                    String   @default("")
  BrandedSearch           Json     @default("{}")
  BrandedKeywordSearch    Json     @default("{}")
  nonBrandedKeywordSearch Json     @default("{}")
  ProspectPDPImage        Json     @default("{}")
  CompetitorSearch        Json     @default("{}")
  createdAt               DateTime @default(now())
  updatedAt               DateTime @default(now())
  company                 Company  @relation(fields: [companyId], references: [id], onDelete: Cascade)

  @@index([companyId])
}

model OutputData {
  id                  Int      @id @default(autoincrement())
  companyName         String
  firstName           String
  secondName          String   @default("")
  position            String   @default("")
  email               String   @default("")
  website             String
  mailData            String   @default("")
  promptTokens        Int      @default(0)
  completionTokens    Int      @default(0)
  aboutDataStatus     String   @default("pending")
  amazonDataStatus    String   @default("pending")
  homepageDataStatus  String   @default("not-found")
  qualificationStatus String   @default("")
  amazonSearchUrl     String   @default("")
  promptTemplate      String   @default("")
  userPrompt          Json     @default("{}")
  gptDetails          String   @default("")
  inputPrice          Float    @default(0.0)
  outputPrice         Float    @default(0.0)
  prospectDetails     Json     @default("{}")
  compKeyPrompt       String   @default("")
  searchKeyword       String   @default("")
  competitorDetails   Json     @default("{}")
  revenueDifference   Float    @default(0.0)
  amazonAudit         Json     @default("{}")
  auditReport         Json     @default("{}")
  companySlug         String   @default("{}")
  productSlug         String   @default("")
  jobId               Int
  companyId           Int
  createdAt           DateTime @default(now())
  updatedAt           DateTime @default(now())
  caseStudies         Json     @default("{}")
  category            Json     @default("{}")
  sellerDetails       Json     @default("{}")
  finalData           Json     @default("{}")
  ppcAudit            Json     @default("{}")
  auditMailData       Json     @default("{}")
  campaignName        String   @default("")
  pricing             Json     @default("{}")
  company             Company  @relation(fields: [companyId], references: [id], onDelete: Cascade)
  job                 Job      @relation(fields: [jobId], references: [id], onDelete: Cascade)

  @@index([jobId])
  @@index([companyId])
  @@index([productSlug])
}

model AmazonAuditReport {
  id                   Int      @id @default(autoincrement())
  companyName          String
  companyId            Int
  sellerEmail          String   @default("")
  productUrl           String   @default("")
  amazonAudit          Json     @default("{}")
  auditReport          Json     @default("{}")
  isProductURLProvided Boolean  @default(false)
  slug                 String   @default("")
  finalUrl             String   @default("")
  pageImage            String   @default("")
  productImage         String   @default("")
  pdfUrl               String   @default("")
  category             String   @default("")
  competitorUrl        String   @default("")
  createdAt            DateTime @default(now())
  updatedAt            DateTime @default(now())
  caseStudies          Json     @default("{}")
  prospectDetails      Json     @default("{}")
  company              Company  @relation(fields: [companyId], references: [id], onDelete: Cascade)

  @@index([companyId])
  @@index([slug])
}

model UTMSync {
  id         Int    @id @default(autoincrement())
  uuid       String
  url        String
  clientName String
  sellerId   String @default("")
  type       String @default("")
  source     String @default("web")
  campaign   String @default("default")

  @@index([type, clientName])
}

model UTMSyncNew {
  id          Int    @id @default(autoincrement())
  uuid        String @unique
  url         String
  redirectUrl String
  clientName  String
  sellerId    String @default("")
  type        String @default("")
  campaign    String @default("")
  email       String @default("")

  @@index([type, clientName])
}

model LexImageGenJob {
  id        Int                 @id @default(autoincrement())
  name      String
  clientId  Int
  status    JobStatus           @default(PENDING)
  createdAt DateTime            @default(now())
  updatedAt DateTime            @updatedAt
  review    LexImageGenReview[] @relation("LexImageGenJobToLexImageGenReview")
}

model LexImageGenReview {
  id             Int              @id @default(autoincrement())
  reviewId       String           @unique
  reviewUrl      String
  asin           String?
  brandName      String?
  violationTagId Int[]
  violationTag   String[]
  status         JobStatus        @default(PENDING)
  imageUrl       String           @default("")
  inputData      Json             @default("{}")
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  LexImageGenJob LexImageGenJob[] @relation("LexImageGenJobToLexImageGenReview")
}

model LexImageGenOutputData {
  id             Int       @id @default(autoincrement())
  jobId          Int
  revId          Int
  reviewUrl      String
  violationTagId Int[]
  violationTag   String[]
  asin           String?
  brandName      String?
  imageUrl       String    @default("")
  status         JobStatus @default(PENDING)
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
}

model LexViolationTag {
  id          Int    @id @default(autoincrement())
  name        String
  imageUrl    String
  description String @default("")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Feature {
  id            Int      @id @default(autoincrement())
  name          String   @unique
  numOfVersions Int      @default(0)
  prompts       Prompt[] // 1-to-many relation
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
}

model Prompt {
  id        Int      @id @default(autoincrement())
  name      String
  featureId Int
  content   String
  type      String
  feature   Feature  @relation(fields: [featureId], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([featureId, name])
}

model PromptLogs {
  id          Int     @id @default(autoincrement())
  ftID        Int
  promptId    Int
  cost        Float
  output      String
  tokenUsage  Json?
  email       String?
  sellerName  String?
  productSlug String?
  clientId    Int
}

enum JobStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  FAILED
}

enum LexJobType {
  SELLER_ID
  SINGLE_ASIN
  BULK_ASIN
  SINGLE_REVIEW
  BULK_REVIEW
}

enum CookieStatus {
  ACTIVE
  EXPIRE
  UNKNOWN
}

enum ScrapingStatus {
  PENDING
  SCRAPED
  FAILED
  REVIEW_PENDING
  REVIEW_IN_PROGRESS
  REVIEW_SCRAPED
}

enum ReviewStatus {
  PENDING
  IN_PROGRESS
  DATA_SCRAPED
  AI_ANALYSIS_PENDING
  VIOLATION_DETECTION_PENDING
  VIOLATION_DETECTION_IN_PROGRESS
  VIOLATION_DETECTED
  NO_VIOLATION_DETECTED
  COMPLETED
  FAILED
}

model LexJob {
  id           Int        @id @default(autoincrement())
  name         String     @unique
  type         LexJobType
  status       JobStatus  @default(PENDING)
  errorMessage String?
  countryCode  String     @default("US")
  sellerId     String?
  asin         String?
  totalAsins   Int?
  totalReviews Int?
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  sellerId_ref LexSeller? @relation("SellerJobs", fields: [sellerId], references: [sellerId])
  asins        LexASIN[]  @relation("JobAsins")

  @@index([type])
  @@index([status])
  @@index([sellerId])
  @@index([asin])
  @@index([createdAt])
}

model LexSeller {
  id          Int            @id @default(autoincrement())
  sellerId    String         @unique
  name        String?
  countryCode String?
  status      ScrapingStatus @default(PENDING)
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt
  asins       LexASIN[] // One-to-many: seller to many ASINs

  jobs LexJob[] @relation("SellerJobs")

  @@index([sellerId])
  @@index([countryCode])
  @@index([status])
}

model LexASIN {
  id          Int        @id @default(autoincrement())
  asin        String
  LexSellerId Int?
  seller      LexSeller? @relation(fields: [LexSellerId], references: [id])

  sellerId     String?
  countryCode  String?
  sellerName   String?
  title        String?
  category     String?
  image        String?
  productLink  String?
  reviewCounts Json?
  avgRating    Float?
  totalReviews Int?
  type         AsinType?
  status       ScrapingStatus @default(PENDING)
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @default(now())

  latestData Json?

  reviews  LexReview[]
  bulkJobs LexJob[]    @relation("JobAsins")

  @@unique([asin, countryCode])
  @@index([asin])
  @@index([sellerId])
  @@index([countryCode])
  @@index([reviewCounts])
  @@index([totalReviews])
  @@index([status])
}

model LexReview {
  id              Int       @id @default(autoincrement())
  asin            String
  asinId          Int
  jobId           Int?
  reviewContent   String?
  reviewTitle     String?
  reviewScore     Float?
  reviewDate      DateTime?
  reviewer        String?
  reviewerCountry String?
  reviewerID      String?
  isVerified      Boolean?
  reviewLink      String?
  reviewerLink    String?
  HelpfulCounts   Int?
  reviewImage     String?
  image1          String?
  image2          String?
  image3          String?
  image4          String?
  pageUrl         String?
  reviewID        String?   @unique
  sellerId        String?

  productTitle String?
  productLink  String?
  variant_0    String?
  variant_1    String?
  status       ReviewStatus @default(PENDING)
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt

  prompt1Output String?
  prompt2Output String?
  prompt3Output String?
  violation     Boolean?

  //Lex Checker fields
  checkerStatus   ReviewCheckerStatus @default(PENDING)
  next_run        DateTime?           
  run_frequency   Int                 @default(0)
  totalRuns       Int                 @default(0)
  removedAt       DateTime?
  removedHistory  Json                @default("[]") // Array of removal dates
  returnedHistory Json                @default("[]") // Array of return dates
  comments        String              @default("") // Comments for status changes

  // New fields for enhanced tracking
  autoPromptEnabled     Boolean  @default(true) // Whether auto prompt execution is enabled
  manualPromptRequested Boolean  @default(false) // Whether manual prompt was requested
  promptExecutionJobId  Int? // Job ID for tracking prompt execution
  violationConfidence   Float? // Confidence score for violation detection
  violationReason       String?
  lastPromptChainId     Int? // ID of the last prompt chain run
  metadata              Json?    @default("{}") // For storing tokens, costs, models, etc.

  asinRef                    LexASIN                      @relation(fields: [asinId], references: [id])
  LexReviewCheckerJob        LexReviewCheckerJob?         @relation(fields: [lexReviewCheckerJobId], references: [id])
  lexReviewCheckerJobId      Int?
  LexReviewCheckerOutputData LexReviewCheckerOutputData[]

  @@index([reviewID])
  @@index([reviewScore])
  @@index([violation])
  @@index([sellerId])
  @@index([asin])
  @@index([status])
  @@index([autoPromptEnabled])
}

model LexReviewCheckerJob {
  id               Int                          @id @default(autoincrement())
  name             String
  status           ReviewJobStatus              @default(PENDING)
  createdAt        DateTime                     @default(now())
  updatedAt        DateTime                     @updatedAt
  Review           LexReview[]
  ReviewOutputData LexReviewCheckerOutputData[]
}

model LexReviewCheckerOutputData {
  id          Int                 @id @default(autoincrement())
  revId       Int
  review      LexReview           @relation(fields: [revId], references: [id], onDelete: Cascade)
  reviewJobId Int
  status      ReviewCheckerStatus @default(PENDING)
  reviewJob   LexReviewCheckerJob @relation(fields: [reviewJobId], references: [id], onDelete: Cascade)
  reviewUrl   String
  createdAt   DateTime            @default(now())
  updatedAt   DateTime            @updatedAt
}

model LexReviewScraperCookies {
  id           Int          @id @default(autoincrement())
  emailId      String
  cookieKey    String
  cookieStatus CookieStatus
  countryCode  String?
  active       Boolean      @default(true)
  lastUsed     DateTime? // Track when cookie was last used  
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @default(now())

  @@index([lastUsed])
  @@index([countryCode, active, cookieStatus])
}

enum AsinType {
  CLIENT
}

model LexPromptChain {
  id          Int      @id @default(autoincrement())
  name        String
  description String?
  prompt1     String // First prompt in the chain
  prompt2     String // Second prompt in the chain  
  prompt3     String // Third prompt in the chain
  model       String   @default("azure-gpt4o") // azure-gpt4o, gemini-2.5-pro, gemini-2.5-flash, deepseek
  isActive    Boolean  @default(true)
  isPrimary   Boolean  @default(false)
  isClient    Boolean  @default(false) // To distinguish client-specific chains
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([isActive])
  @@index([isPrimary])
  @@index([model])
  @@index([isClient])
}

enum ReviewJobStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}

enum ReviewCheckerStatus {
  PENDING
  PRESENT
  REMOVED
  FAILED
  RESURRECTED
}
