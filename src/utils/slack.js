const axios = require("axios");
const { formatDateOnly, formatTimeOnly } = require("./utils");
const { cleanReply } = require("../services/replyAutomation/sheet");

async function sendMessageToSlack(url, message) {
  try {
    const response = await axios.post(url, { text: message });

    if (response.status === 200) {
      console.log("Message sent successfully");
    } else {
      console.error(
        "Failed to send message:",
        response.status,
        response.statusText
      );
      await logErrorToSlack(
        url,
        `Slack message failed: ${response.statusText}`
      );
    }
  } catch (error) {
    console.error("Error sending message to Slack:", error.message);
    await logErrorToSlack(url, `Error sending message: ${error.message}`);
  }
}

async function sendBlockToSlack(url, data, client_name) {
  const campaign_text = cleanReply(data?.reply_message?.html || "");
  const maxRetries = 3; // Number of retries
  const retryDelay = [2000, 4000, 8000]; // Backoff times in ms

  try {
    // Slack payload size limit (1 MB, but we stay far below this)
    const maxMessageLength = 1000;
    const chunks = [];

    // Break the campaign_text into smaller chunks
    if (campaign_text.length > maxMessageLength) {
      for (let i = 0; i < campaign_text.length; i += maxMessageLength) {
        chunks.push(campaign_text.substring(i, i + maxMessageLength));
      }
    } else {
      chunks.push(campaign_text);
    }

    // Send the initial block with metadata
    const initialBlocks = createSlackBlocks(
      data,
      chunks.length > 1
        ? "See additional messages for full details"
        : campaign_text,
      client_name
    );

    await sendWithRetry(
      url,
      { text: "Data notification", blocks: initialBlocks },
      maxRetries,
      retryDelay
    );

    if (chunks.length > 1) {
      for (const [index, chunk] of chunks.entries()) {
        const chunkMessage = {
          text: `*Reply Content (Part ${index + 1}/${
            chunks.length
          }):*\n${chunk}`,
          blocks: [
            {
              type: "section",
              text: {
                type: "mrkdwn",
                text: `*Reply Content (Part ${index + 1}/${
                  chunks.length
                }):*\n${chunk}`,
              },
            },
          ],
        };

        await sendWithRetry(url, chunkMessage, maxRetries, retryDelay);
        console.log(`Chunk ${index + 1} sent successfully`);
      }
    }
  } catch (error) {
    console.error("Error sending message to Slack:", error.message);
    await logErrorToSlack(url, `Slack message failed: ${error.message}`);
  }
}

async function sendWithRetry(url, payload, maxRetries = 3, baseDelay = 1000) {
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const res = await axios.post(url, payload, { timeout: 15000 });

      // Only retry on transient errors
      if (res.status >= 200 && res.status < 300) return res.data;
      if (res.status >= 400 && res.status < 500) {
        throw new Error(`Permanent failure: HTTP ${res.status} - ${res.statusText}`);
      }

      throw new Error(`Transient failure: HTTP ${res.status} - ${res.statusText}`);
    } catch (error) {
      const isLastAttempt = attempt === maxRetries;
      const isTransientError =
        error.response &&
        [500, 502, 503, 504, 429].includes(error.response.status);

      if (!isLastAttempt && isTransientError) {
        const delay = baseDelay * 2 ** attempt; // Exponential backoff
        console.warn(`Retry ${attempt + 1}/${maxRetries} after error: ${error.message}`);
        await new Promise((resolve) => setTimeout(resolve, delay));
      } else {
        console.error("Max retries reached. Failed to send Slack message:", error.message);
        try {
          await logErrorToSlack(url, `Slack message failed: ${error.message}`);
        } catch (logError) {
          console.error("Failed to log error to Slack:", logError.message);
        }
        return { success: false, error: error.message };
      }
    }
  }
}

async function logErrorToSlack(url, errorMessage) {
  try {
    await axios.post(
      url,
      { text: `🚨 *Error Alert!* 🚨\n${errorMessage}` },
      { timeout: 15000 }
    );
  } catch (notifyError) {
    console.error(
      "Failed to send error notification to Slack:",
      notifyError.message
    );
  }
}

async function sendServerNotification(eventType, additionalInfo = "") {
  const webhookUrl = process.env.LEX_NOTI_SLACK_WEBHOOK_URL;
  const TAGGED_USERS = ["<@U088F8THDT6>", "<@U0765PL0W4V>"];

  if (!webhookUrl) {
    console.warn("Slack webhook URL not set for server notifications");
    return;
  }

  let message = {};
  const timestamp = new Date().toLocaleString();
  const serverId = process.env.SERVER_ID || "Unknown";
  const serverQueue = process.env.SERVER_QUEUE || "Unknown";

  switch (eventType) {
    case "START":
      message = {
        text: `🚀 *Server Started Successfully!* ${TAGGED_USERS?.join(" ")}\n\n` +
              `*Server ID:* ${serverId}\n` +
              `*Server Queue:* ${serverQueue}\n` +
              `*Start Time:* ${timestamp}\n` +
              `*Environment:* ${process.env.NODE_ENV || "development"}\n` +
              `${additionalInfo ? `*Additional Info:* ${additionalInfo}\n` : ""}` +
              `\n✅ Lex scraping system is now active and monitoring for jobs.`
      };
      break;

    case "STOP":
      message = {
        text: `🛑 *Server Stopping...* ${TAGGED_USERS?.join(" ")}\n\n` +
              `*Server ID:* ${serverId}\n` +
              `*Server Queue:* ${serverQueue}\n` +
              `*Stop Time:* ${timestamp}\n` +
              `*Environment:* ${process.env.NODE_ENV || "development"}\n` +
              `${additionalInfo ? `*Additional Info:* ${additionalInfo}\n` : ""}` +
              `\n⚠️ Lex scraping system is shutting down gracefully.`
      };
      break;

    case "SELLER_SCRAPING_COMPLETE":
      message = {
        text: `🛍️ *Seller ASIN Scraping Complete!* ${TAGGED_USERS?.join(" ")}\n\n` +
              `*Server ID:* ${serverId}\n` +
              `*Time:* ${timestamp}\n` +
              `${additionalInfo}`
      };
      break;

    case "BULK_ASIN_SCRAPING_COMPLETE":
      message = {
        text: `📦 *Bulk ASIN Scraping Complete!* ${TAGGED_USERS?.join(" ")}\n\n` +
              `*Server ID:* ${serverId}\n` +
              `*Time:* ${timestamp}\n` +
              `${additionalInfo}`
      };
      break;

    case "SINGLE_ASIN_SCRAPING_COMPLETE":
      message = {
        text: `📦 *Single ASIN Scraping Complete!* ${TAGGED_USERS?.join(" ")}\n\n` +
              `*Server ID:* ${serverId}\n` +
              `*Time:* ${timestamp}\n` +
              `${additionalInfo}`
      };
      break;

    case "SINGLE_REVIEW_SCRAPING_COMPLETE":
      message = {
        text: `📝 *Single ASIN Review Scraping Complete!* ${TAGGED_USERS?.join(" ")}\n\n` +
              `*Server ID:* ${serverId}\n` +
              `*Time:* ${timestamp}\n` +
              `${additionalInfo}`
      };
      break;

    case "BULK_REVIEW_SCRAPING_COMPLETE":
      message = {
        text: `📝 *Bulk Review Scraping Complete!* ${TAGGED_USERS?.join(" ")}\n\n` +
              `*Server ID:* ${serverId}\n` +
              `*Time:* ${timestamp}\n` +
              `${additionalInfo}`
      };
      break;

    case "SELLER_UPLOAD_COMPLETE":
      message = {
        text: `🛍️ *Seller ID Upload Complete!* ${TAGGED_USERS?.join(" ")}\n\n` +
              `*Server ID:* ${serverId}\n` +
              `*Time:* ${timestamp}\n` +
              `${additionalInfo}`
      };
      break;

    case "ASIN_UPLOAD_COMPLETE":
      message = {
        text: `📦 *ASIN List Upload Complete!* ${TAGGED_USERS?.join(" ")}\n\n` +
              `*Server ID:* ${serverId}\n` +
              `*Time:* ${timestamp}\n` +
              `${additionalInfo}`
      };
      break;

    default:
      return; // Don't send any other notification types
  }

  try {
    await axios.post(webhookUrl, message);
    console.log(`Server ${eventType.toLowerCase()} notification sent to Slack`);
  } catch (error) {
    console.error("Error sending server notification to Slack:", error);
  }
}

function createSlackBlocks(data, campaign_text, client_name) {
  return [
    {
      type: "section",
      text: {
        type: "mrkdwn",
        text: "*📋 * _Client Information_",
      },
    },
    {
      type: "section",
      fields: [
        {
          type: "mrkdwn",
          text: `*Client Name:*\n${client_name || "Undefined"}`,
        },
        {
          type: "mrkdwn",
          text: `*Campaign Name:*\n${data.campaign_name || "Undefined"}`,
        },
      ],
    },
    {
      type: "section",
      text: {
        type: "mrkdwn",
        text: "*👤 * _Prospect Details_",
      },
    },
    {
      type: "section",
      fields: [
        {
          type: "mrkdwn",
          text: `*Prospect Name:*\n${data.to_name || "Undefined"}`,
        },
        {
          type: "mrkdwn",
          text: `*Prospect Email ID:*\n${data.to_email || "Undefined"}`,
        },
      ],
    },
    {
      type: "section",
      text: {
        type: "mrkdwn",
        text: "*📝 * _Reply Details_",
      },
    },
    {
      type: "section",
      fields: [
        {
          type: "mrkdwn",
          text: `*Reply Sentiment:*\n${
            data?.lead_category?.new_name || "Undefined"
          }`,
        },
      ],
    },
    {
      type: "section",
      text: {
        type: "mrkdwn",
        text: `*Reply Content:*\n${campaign_text || "Undefined"}`,
      },
    },
    {
      type: "section",
      text: {
        type: "mrkdwn",
        text: "*📅 * _Reply Timing_",
      },
    },
    {
      type: "section",
      fields: [
        {
          type: "mrkdwn",
          text: `*Date & Day We Got the Reply:*\n${
            formatDateOnly(data?.reply_message?.time) ||
            formatDateOnly(data?.sent_message?.time) ||
            "Undefined"
          }`,
        },
        {
          type: "mrkdwn",
          text: `*Time We Got the Reply:*\n${
            formatTimeOnly(data?.reply_message?.time) ||
            formatTimeOnly(data?.sent_message?.time) ||
            "Undefined"
          }`,
        },
      ],
    },
    {
      type: "divider",
    },
    {
      type: "context",
      elements: [
        {
          type: "image",
          image_url: "https://avatars.githubusercontent.com/u/5245089?v=4",
          alt_text: "Reply Automation",
        },
        {
          type: "mrkdwn",
          text: "Author: <https://www.smartlead.ai|*Reply Automation*>",
        },
      ],
    },
  ];
}

// // Example usage
// const message = 'Here is the PDF you requested: https://eq--assets.s3.ap-south-1.amazonaws.com/pdfs/flextech-solutions-llc.pdf';

// sendMessageToSlack(webhookUrl, message);
module.exports = { sendMessageToSlack, sendBlockToSlack, sendServerNotification };
